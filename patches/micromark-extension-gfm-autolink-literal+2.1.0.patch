diff --git a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
index 904e272..83608ee 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js
@@ -654,7 +654,30 @@ function tokenizePath(effects, ok) {
       code === codes.questionMark ||
       code === codes.rightSquareBracket ||
       code === codes.underscore ||
-      code === codes.tilde
+      code === codes.tilde ||
+      code === 65281 || // ！
+      code === 65282 || // ＂
+      code === 65287 || // ＇
+      code === 65289 || // ）
+      code === 65292 || // ，
+      code === 65294 || // ．
+      code === 65306 || // ：
+      code === 65307 || // ；
+      code === 65311 || // ？
+      code === 65341 || // ］
+      code === 65343 || // ＿
+      code === 65373 || // ｝
+      code === 65374 || // ～
+      code === 12289 || // 、
+      code === 12290 || // 。
+      code === 12299 || // 》
+      code === 12301 || // 」
+      code === 12303 || // 』
+      code === 12305 || // 】
+      code === 8230 ||  // …
+      code === 8212 ||  // —
+      code === 8217 ||  // '
+      code === 8221     // "
     ) {
       return effects.check(trail, ok, pathAtPunctuation)(code)
     }
@@ -734,7 +757,30 @@ function tokenizeTrail(effects, ok, nok) {
       code === codes.semicolon ||
       code === codes.questionMark ||
       code === codes.underscore ||
-      code === codes.tilde
+      code === codes.tilde ||
+      code === 65281 || // ！
+      code === 65282 || // ＂
+      code === 65287 || // ＇
+      code === 65289 || // ）
+      code === 65292 || // ，
+      code === 65294 || // ．
+      code === 65306 || // ：
+      code === 65307 || // ；
+      code === 65311 || // ？
+      code === 65341 || // ］
+      code === 65343 || // ＿
+      code === 65373 || // ｝
+      code === 65374 || // ～
+      code === 12289 || // 、
+      code === 12290 || // 。
+      code === 12299 || // 》
+      code === 12301 || // 」
+      code === 12303 || // 』
+      code === 12305 || // 】
+      code === 8230 ||  // …
+      code === 8212 ||  // —
+      code === 8217 ||  // '
+      code === 8221     // "
     ) {
       effects.consume(code)
       return trail
diff --git a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
index dffbaf1..2d94f64 100644
--- a/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
+++ b/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js
@@ -581,7 +581,7 @@ function tokenizePath(effects, ok) {
     // Check whether this trailing punctuation marker is optionally
     // followed by more trailing markers, and then followed
     // by an end.
-    if (code === 33 || code === 34 || code === 38 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 60 || code === 63 || code === 93 || code === 95 || code === 126) {
+    if (code === 33 || code === 34 || code === 38 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 60 || code === 63 || code === 93 || code === 95 || code === 126 || code === 65281 || code === 65282 || code === 65287 || code === 65289 || code === 65292 || code === 65294 || code === 65306 || code === 65307 || code === 65311 || code === 65341 || code === 65343 || code === 65373 || code === 65374 || code === 12289 || code === 12290 || code === 12299 || code === 12301 || code === 12303 || code === 12305 || code === 8230 || code === 8212 || code === 8217 || code === 8221) {
       return effects.check(trail, ok, pathAtPunctuation)(code);
     }
     if (code === null || markdownLineEndingOrSpace(code) || unicodeWhitespace(code)) {
@@ -641,7 +641,7 @@ function tokenizeTrail(effects, ok, nok) {
    */
   function trail(code) {
     // Regular trailing punctuation.
-    if (code === 33 || code === 34 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 63 || code === 95 || code === 126) {
+    if (code === 33 || code === 34 || code === 39 || code === 41 || code === 42 || code === 44 || code === 46 || code === 58 || code === 59 || code === 63 || code === 95 || code === 126 || code === 65281 || code === 65282 || code === 65287 || code === 65289 || code === 65292 || code === 65294 || code === 65306 || code === 65307 || code === 65311 || code === 65341 || code === 65343 || code === 65373 || code === 65374 || code === 12289 || code === 12290 || code === 12299 || code === 12301 || code === 12303 || code === 12305 || code === 8230 || code === 8212 || code === 8217 || code === 8221) {
       effects.consume(code);
       return trail;
     }
