# Patches

## micromark-gfm-autolink-literal-chinese-parenthesis.patch

### 问题描述
在使用 micromark-extension-gfm-autolink-literal 进行URL自动链接识别时，中文右括号）不被识别为尾随标点符号，导致URL链接可能包含不应该包含的中文括号。

### 解决方案
在 `node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js` 中的两个位置添加中文右括号）的Unicode码点65289：

1. `pathInside` 函数中的尾随标点符号检测
2. `trail` 函数中的常规尾随标点符号处理

### 修改内容
- 在第584行的条件判断中添加 `|| code === 65289`
- 在第644行的条件判断中添加 `|| code === 65289`

### 使用方法
```bash
# 应用patch
patch -p1 < patches/micromark-gfm-autolink-literal-chinese-parenthesis.patch

# 或者手动修改文件
# 在相应位置添加 || code === 65289
```

### 测试用例
修改后，以下文本应该正确识别URL边界：
- `访问 https://example.com）查看更多信息` - 中文右括号不应包含在链接中
- `链接：https://example.com）。` - 链接应该在中文右括号前结束

### 注意事项
- 这是对第三方包的修改，在更新包时需要重新应用patch
- Unicode码点65289对应中文全角右括号）
- 建议在项目的postinstall脚本中自动应用此patch
