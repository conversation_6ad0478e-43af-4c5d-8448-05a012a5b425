#!/usr/bin/env node

/**
 * 全角符号定义
 * 用于URL自动链接识别中的尾随标点符号处理
 */

// 常见的全角符号及其Unicode码点
const FULLWIDTH_SYMBOLS = {
  // 全角标点符号
  65281: '！', // 全角感叹号
  65282: '＂', // 全角双引号
  65283: '＃', // 全角井号
  65284: '＄', // 全角美元符号
  65285: '％', // 全角百分号
  65286: '＆', // 全角和号
  65287: '＇', // 全角单引号
  65288: '（', // 全角左括号
  65289: '）', // 全角右括号
  65290: '＊', // 全角星号
  65291: '＋', // 全角加号
  65292: '，', // 全角逗号
  65293: '－', // 全角减号
  65294: '．', // 全角句号
  65295: '／', // 全角斜杠
  
  // 全角冒号和分号
  65306: '：', // 全角冒号
  65307: '；', // 全角分号
  
  // 全角比较符号
  65308: '＜', // 全角小于号
  65309: '＝', // 全角等号
  65310: '＞', // 全角大于号
  65311: '？', // 全角问号
  65312: '＠', // 全角at符号
  
  // 全角方括号
  65339: '［', // 全角左方括号
  65340: '＼', // 全角反斜杠
  65341: '］', // 全角右方括号
  65342: '＾', // 全角脱字符
  65343: '＿', // 全角下划线
  65344: '｀', // 全角反引号
  
  // 全角花括号
  65371: '｛', // 全角左花括号
  65372: '｜', // 全角竖线
  65373: '｝', // 全角右花括号
  65374: '～', // 全角波浪号
  
  // 中文标点符号
  12289: '、', // 顿号
  12290: '。', // 句号
  12298: '《', // 左书名号
  12299: '》', // 右书名号
  12300: '「', // 左单引号
  12301: '」', // 右单引号
  12302: '『', // 左双引号
  12303: '』', // 右双引号
  12304: '【', // 左方头括号
  12305: '】', // 右方头括号
  
  // 其他常见中文标点
  65374: '～', // 波浪号
  8230: '…',  // 省略号
  8212: '—',  // 破折号
  8216: '\u2018',  // 左单引号 '
  8217: '\u2019',  // 右单引号 '
  8220: '\u201C',  // 左双引号 "
  8221: '\u201D',  // 右双引号 "
};

// 获取所有全角符号的Unicode码点数组
const FULLWIDTH_CODES = Object.keys(FULLWIDTH_SYMBOLS).map(code => parseInt(code));

// 获取尾随标点符号的码点（通常出现在URL末尾的符号）
const TRAILING_PUNCTUATION_CODES = [
  65281, // ！
  65282, // ＂
  65287, // ＇
  65289, // ）
  65292, // ，
  65294, // ．
  65306, // ：
  65307, // ；
  65311, // ？
  65341, // ］
  65343, // ＿
  65373, // ｝
  65374, // ～
  12289, // 、
  12290, // 。
  12299, // 》
  12301, // 」
  12303, // 』
  12305, // 】
  8230,  // …
  8212,  // —
  8217,  // '
  8221,  // "
];

module.exports = {
  FULLWIDTH_SYMBOLS,
  FULLWIDTH_CODES,
  TRAILING_PUNCTUATION_CODES
};

// 如果直接运行此脚本，打印所有符号
if (require.main === module) {
  console.log('🔤 全角符号列表：');
  console.log('==================');
  
  Object.entries(FULLWIDTH_SYMBOLS).forEach(([code, symbol]) => {
    console.log(`${code.padStart(5)}: ${symbol} (${symbol.charCodeAt(0)})`);
  });
  
  console.log('\n📋 尾随标点符号码点：');
  console.log('==================');
  console.log(TRAILING_PUNCTUATION_CODES.join(', '));
}
