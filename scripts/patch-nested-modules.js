#!/usr/bin/env node

/**
 * 自定义脚本：为嵌套的 node_modules 中的包打补丁
 * 用于修复全角符号在URL自动链接识别中的问题
 */

const fs = require('fs');
const path = require('path');
const { TRAILING_PUNCTUATION_CODES } = require('./fullwidth-symbols');

// 需要修补的文件路径
const filesToPatch = [
  'node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js',
  'node_modules/micromark-extension-gfm-autolink-literal/dev/lib/syntax.js',
  'node_modules/@ht/doc-code-playground/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js'
];

function patchFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在，跳过: ${filePath}`);
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 检查是否已经包含全角符号补丁
    const hasFullwidthPatch = TRAILING_PUNCTUATION_CODES.some(code => 
      content.includes(`code === ${code}`)
    );

    if (hasFullwidthPatch) {
      console.log(`ℹ️  文件已经包含全角符号补丁: ${filePath}`);
      return false;
    }

    // 为lib版本（数字码点格式）添加全角符号
    const libPathPattern = /(code === 126)(\s*\)\s*\{\s*return effects\.check\(trail, ok, pathAtPunctuation\)\(code\))/;
    if (libPathPattern.test(content)) {
      const fullwidthConditions = TRAILING_PUNCTUATION_CODES.map(code => `code === ${code}`).join(' || ');
      content = content.replace(libPathPattern, `$1 || ${fullwidthConditions}$2`);
      modified = true;
      console.log(`✅ 已修补lib版本 pathInside: ${filePath}`);
    }

    const libTrailPattern = /(code === 126)(\s*\)\s*\{\s*effects\.consume\(code\)\s*return trail)/;
    if (libTrailPattern.test(content)) {
      const fullwidthConditions = TRAILING_PUNCTUATION_CODES.map(code => `code === ${code}`).join(' || ');
      content = content.replace(libTrailPattern, `$1 || ${fullwidthConditions}$2`);
      modified = true;
      console.log(`✅ 已修补lib版本 trail: ${filePath}`);
    }

    // 为dev版本（codes常量格式）添加全角符号
    const devPathPattern = /(code === codes\.tilde)(\s*\)\s*\{\s*return effects\.check\(trail, ok, pathAtPunctuation\)\(code\))/;
    if (devPathPattern.test(content)) {
      const fullwidthConditions = TRAILING_PUNCTUATION_CODES.map(code => 
        `\n      code === ${code}`
      ).join(' ||');
      content = content.replace(devPathPattern, `$1 ||${fullwidthConditions} // 全角符号$2`);
      modified = true;
      console.log(`✅ 已修补dev版本 pathInside: ${filePath}`);
    }

    const devTrailPattern = /(code === codes\.tilde)(\s*\)\s*\{\s*effects\.consume\(code\)\s*return trail)/;
    if (devTrailPattern.test(content)) {
      const fullwidthConditions = TRAILING_PUNCTUATION_CODES.map(code => 
        `\n      code === ${code}`
      ).join(' ||');
      content = content.replace(devTrailPattern, `$1 ||${fullwidthConditions} // 全角符号$2`);
      modified = true;
      console.log(`✅ 已修补dev版本 trail: ${filePath}`);
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🎉 成功修补文件: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  文件无需修补或格式不匹配: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修补文件失败: ${filePath}`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 开始修补 micromark-extension-gfm-autolink-literal 以支持全角符号...\n');
  console.log(`📋 将添加 ${TRAILING_PUNCTUATION_CODES.length} 个全角符号的支持\n`);
  
  let patchedCount = 0;
  
  filesToPatch.forEach(filePath => {
    if (patchFile(filePath)) {
      patchedCount++;
    }
  });
  
  console.log(`\n✨ 修补完成！共修补了 ${patchedCount} 个文件`);
  console.log('现在全角符号将被正确识别为URL的尾随标点符号');
  console.log('\n📋 已修补的文件版本：');
  console.log('  • lib/syntax.js - 生产版本（数字码点格式）');
  console.log('  • dev/lib/syntax.js - 开发调试版本（codes常量格式）');
  console.log('  • @ht/doc-code-playground 嵌套依赖版本');
  
  console.log('\n🔤 支持的全角符号包括：');
  console.log('  ！＂＇），．：；？］＿｝～、。》」』】…—\'"');
}

if (require.main === module) {
  main();
}

module.exports = { patchFile, filesToPatch };
