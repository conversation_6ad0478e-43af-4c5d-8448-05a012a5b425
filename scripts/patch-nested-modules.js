#!/usr/bin/env node

/**
 * 自定义脚本：为嵌套的 node_modules 中的包打补丁
 * 用于修复中文右括号）在URL自动链接识别中的问题
 */

const fs = require('fs');
const path = require('path');

// 需要修补的文件路径
const filesToPatch = [
  'node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js',
  'node_modules/@ht/doc-code-playground/node_modules/micromark-extension-gfm-autolink-literal/lib/syntax.js'
];

// 中文右括号的Unicode码点
const CHINESE_RIGHT_PARENTHESIS = 65289;

function patchFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在，跳过: ${filePath}`);
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // 修补 pathInside 函数中的条件判断
    const pathInsidePattern = /(code === 126)(\s*\)\s*\{\s*return effects\.check\(trail, ok, pathAtPunctuation\)\(code\))/;
    if (pathInsidePattern.test(content) && !content.includes(`code === ${CHINESE_RIGHT_PARENTHESIS}`)) {
      content = content.replace(pathInsidePattern, `$1 || code === ${CHINESE_RIGHT_PARENTHESIS}$2`);
      modified = true;
      console.log(`✅ 已修补 pathInside 函数: ${filePath}`);
    }

    // 修补 trail 函数中的条件判断
    const trailPattern = /(code === 126\s*\)\s*\{\s*effects\.consume\(code\)\s*return trail)/;
    if (trailPattern.test(content) && !content.includes(`|| code === ${CHINESE_RIGHT_PARENTHESIS}`)) {
      content = content.replace(trailPattern, `|| code === ${CHINESE_RIGHT_PARENTHESIS}$1`);
      modified = true;
      console.log(`✅ 已修补 trail 函数: ${filePath}`);
    }

    // 处理多行格式的条件判断（版本1.0.5格式）
    const multiLinePattern = /(code === 126\s*\)\s*\{\s*return effects\.check\(trail, ok, pathAtPunctuation\)\(code\))/;
    if (multiLinePattern.test(content) && !content.includes(`code === ${CHINESE_RIGHT_PARENTHESIS}`)) {
      content = content.replace(/(code === 126)(\s*\)\s*\{\s*return effects\.check\(trail, ok, pathAtPunctuation\)\(code\))/, 
        `$1 ||\n      code === ${CHINESE_RIGHT_PARENTHESIS}$2`);
      modified = true;
      console.log(`✅ 已修补多行格式 pathInside: ${filePath}`);
    }

    // 处理多行格式的 trail 函数
    const multiLineTrailPattern = /(code === 126\s*\)\s*\{\s*effects\.consume\(code\)\s*return trail)/;
    if (multiLineTrailPattern.test(content) && !content.includes(`|| code === ${CHINESE_RIGHT_PARENTHESIS}`)) {
      content = content.replace(/(code === 126)(\s*\)\s*\{\s*effects\.consume\(code\)\s*return trail)/, 
        `$1 ||\n      code === ${CHINESE_RIGHT_PARENTHESIS}$2`);
      modified = true;
      console.log(`✅ 已修补多行格式 trail: ${filePath}`);
    }

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`🎉 成功修补文件: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  文件已经包含补丁或无需修补: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修补文件失败: ${filePath}`, error.message);
    return false;
  }
}

function main() {
  console.log('🔧 开始修补 micromark-extension-gfm-autolink-literal 以支持中文右括号）...\n');
  
  let patchedCount = 0;
  
  filesToPatch.forEach(filePath => {
    if (patchFile(filePath)) {
      patchedCount++;
    }
  });
  
  console.log(`\n✨ 修补完成！共修补了 ${patchedCount} 个文件`);
  console.log('现在中文右括号）将被正确识别为URL的尾随标点符号');
}

if (require.main === module) {
  main();
}

module.exports = { patchFile, filesToPatch };
