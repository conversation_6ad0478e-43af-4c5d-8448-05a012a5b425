<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试全角符号URL自动链接识别</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 15px 0;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-input {
            font-family: 'Courier New', monospace;
            background: #fff;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin: 8px 0;
            font-size: 14px;
        }
        .symbol-info {
            color: #666;
            font-size: 12px;
            margin-top: 5px;
        }
        .category {
            background: #e8f4fd;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .category h3 {
            margin-top: 0;
            color: #1976D2;
        }
    </style>
</head>
<body>
    <h1>🔤 测试全角符号URL自动链接识别</h1>
    
    <p>这个页面用于测试我们的补丁是否正确处理了所有全角符号作为URL的尾随标点符号。</p>
    
    <div class="category">
        <h3>全角标点符号</h3>
        
        <div class="test-case">
            <div class="test-input">访问 https://example.com！获取更多信息</div>
            <div class="symbol-info">全角感叹号 ！ (65281)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接"https://github.com"很有用</div>
            <div class="symbol-info">全角双引号 ＂ (65282)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">请访问'https://www.google.com'了解详情</div>
            <div class="symbol-info">全角单引号 ＇ (65287)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">网站（https://example.com）提供服务</div>
            <div class="symbol-info">全角右括号 ） (65289)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接：https://example.com，点击访问</div>
            <div class="symbol-info">全角逗号 ， (65292)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">访问 https://example.com．</div>
            <div class="symbol-info">全角句号 ． (65294)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">网址：https://example.com：8080</div>
            <div class="symbol-info">全角冒号 ： (65306)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接；https://example.com；备注</div>
            <div class="symbol-info">全角分号 ； (65307)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">是否访问 https://example.com？</div>
            <div class="symbol-info">全角问号 ？ (65311)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">参考［https://example.com］</div>
            <div class="symbol-info">全角右方括号 ］ (65341)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接＿https://example.com＿强调</div>
            <div class="symbol-info">全角下划线 ＿ (65343)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">配置｛https://example.com｝</div>
            <div class="symbol-info">全角右花括号 ｝ (65373)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接～https://example.com～</div>
            <div class="symbol-info">全角波浪号 ～ (65374)</div>
        </div>
    </div>
    
    <div class="category">
        <h3>中文标点符号</h3>
        
        <div class="test-case">
            <div class="test-input">链接、https://example.com、备注</div>
            <div class="symbol-info">顿号 、 (12289)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">访问 https://example.com。</div>
            <div class="symbol-info">句号 。 (12290)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">参考《https://example.com》</div>
            <div class="symbol-info">右书名号 》 (12299)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接「https://example.com」</div>
            <div class="symbol-info">右单引号 」 (12301)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">参考『https://example.com』</div>
            <div class="symbol-info">右双引号 』 (12303)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">注释【https://example.com】</div>
            <div class="symbol-info">右方头括号 】 (12305)</div>
        </div>
    </div>
    
    <div class="category">
        <h3>其他常见符号</h3>
        
        <div class="test-case">
            <div class="test-input">链接 https://example.com…更多内容</div>
            <div class="symbol-info">省略号 … (8230)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接——https://example.com——重要</div>
            <div class="symbol-info">破折号 — (8212)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接'https://example.com'有用</div>
            <div class="symbol-info">右单引号 ' (8217)</div>
        </div>
        
        <div class="test-case">
            <div class="test-input">链接"https://example.com"推荐</div>
            <div class="symbol-info">右双引号 " (8221)</div>
        </div>
    </div>

    <div class="category">
        <h3>📋 测试说明</h3>
        <p><strong>期望结果：</strong>所有URL链接都应该在相应的全角符号前正确结束，不包含这些符号。</p>
        <p><strong>测试方法：</strong>在你的Milkdown编辑器中输入这些测试用例，观察URL自动链接识别的行为。</p>
        <p><strong>支持的符号：</strong>共支持19个全角符号的正确识别。</p>
    </div>

    <script>
        console.log('🔤 全角符号URL自动链接测试用例已准备就绪');
        console.log('请在你的 Milkdown 编辑器中测试这些用例');
        
        // 统计测试用例数量
        const testCases = document.querySelectorAll('.test-case');
        console.log(`📊 共有 ${testCases.length} 个测试用例`);
    </script>
</body>
</html>
