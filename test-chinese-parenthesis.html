<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试中文右括号URL自动链接识别</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-input {
            font-family: monospace;
            background: #fff;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin: 10px 0;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 3px;
        }
        .expected {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <h1>测试中文右括号URL自动链接识别</h1>
    
    <p>这个页面用于测试我们的补丁是否正确处理了中文右括号）作为URL的尾随标点符号。</p>
    
    <div class="test-case">
        <h3>测试用例 1：基本中文右括号</h3>
        <div class="test-input">访问 https://example.com）查看更多信息</div>
        <div class="expected">期望：链接应该在中文右括号前结束</div>
        <div class="test-result" id="result1"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 2：句末中文右括号</h3>
        <div class="test-input">链接：https://github.com）。</div>
        <div class="expected">期望：链接应该在中文右括号前结束，不包含句号</div>
        <div class="test-result" id="result2"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 3：多个标点符号</h3>
        <div class="test-input">请访问（https://www.google.com）了解详情。</div>
        <div class="expected">期望：链接应该在中文右括号前结束</div>
        <div class="test-result" id="result3"></div>
    </div>
    
    <div class="test-case">
        <h3>测试用例 4：英文右括号对比</h3>
        <div class="test-input">访问 https://example.com) 查看更多信息</div>
        <div class="expected">期望：链接应该在英文右括号前结束（原有功能）</div>
        <div class="test-result" id="result4"></div>
    </div>

    <script>
        // 这里只是一个静态测试页面
        // 实际的测试需要在你的应用中进行，因为需要 Milkdown 编辑器来处理 Markdown
        document.getElementById('result1').innerHTML = '请在你的应用中测试这些用例';
        document.getElementById('result2').innerHTML = '请在你的应用中测试这些用例';
        document.getElementById('result3').innerHTML = '请在你的应用中测试这些用例';
        document.getElementById('result4').innerHTML = '请在你的应用中测试这些用例';
        
        console.log('测试用例已准备就绪，请在你的 Milkdown 编辑器中输入这些文本来测试补丁效果');
    </script>
</body>
</html>
